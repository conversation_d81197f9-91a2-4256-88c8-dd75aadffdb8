import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';
import { auth, googleProvider, microsoftProvider } from '../../firebase';
import { signInWithPopup, fetchSignInMethodsForEmail } from 'firebase/auth';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    const success = login(email, password);
    if (success) {
      // Navigate to reading screen after successful login
      navigate('/readingScreen');
    } else {
      setError('Invalid credentials. Please try again.');
    }
  };

  // Generic OAuth sign-in handler that works with any provider
  const handleOAuthSignIn = async (provider, providerName) => {
    try {
      setError('');
      const result = await signInWithPopup(auth, provider);

      if (!result.user.email) {
        setError(`Unable to use ${providerName} account without an email address.`);
        return;
      }

      // Check if this email already exists with different providers
      try {
        const methods = await fetchSignInMethodsForEmail(auth, result.user.email);
        console.log(`Sign-in methods for ${result.user.email}:`, methods);

        // We'll use our unified authentication system regardless of what Firebase returns
        // This allows us to link accounts by email even if Firebase doesn't support it directly
      } catch (methodsError) {
        console.warn("Error checking sign-in methods:", methodsError);
        // Continue with sign-in even if we can't check methods
      }

      // Use our enhanced third-party login with provider info
      const user = useAuthStore.getState().thirdPartyLogin(
        result.user,
        provider === googleProvider ? 'google' : 'microsoft'
      );

      console.log(`User signed in with ${providerName}:`, {
        user,
        email: result.user.email,
        providers: user.providers,
        operationType: result.operationType
      });

      navigate('/readingScreen');
    } catch (error) {
      console.error(`Error signing in with ${providerName}:`, error);

      // Handle specific error cases
      if (error.code === 'auth/account-exists-with-different-credential') {
        setError(`An account already exists with the same email address but different sign-in credentials.
                 Try signing in using the original provider you used to create the account.`);
      } else {
        setError(`Error signing in with ${providerName}: ${error.message}`);
      }
    }
  };

  // Provider-specific handlers that use the generic handler
  const handleGoogleSignIn = () => handleOAuthSignIn(googleProvider, 'Google');
  const handleMicrosoftSignIn = () => handleOAuthSignIn(microsoftProvider, 'Microsoft');

  return (
    <div className="login-container">

      <Header />
      <AnimationBox className="login-box">

        <h4 className='h4'>Sign In</h4>

        <div className='social-login'>
          <button className='social-btn google body2 ' onClick={handleGoogleSignIn}>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='google' /></button>
          <button className='social-btn microsoft body2' onClick={handleMicrosoftSignIn}>Continue with Microsoft<img src='/icons/microsoft.svg' alt='google' /></button>
          <button className='social-btn facebook body2'>Continue with Facebook<img src='/icons/facebook.svg' alt='google' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error.includes('email') && <div className="error-message">{error}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."
                required
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
            {error && !error.includes('email') && (
              <div className="error-message">{error}</div>)}
          </div>


          <button type="submit" className="body3-bold login-button">Sign in</button>
        </form>
        <div className='body4 forgot-password'><a href="/forgot-password">Forgot password?</a></div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>New to the platform?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/signup')}
          >
            Sign up
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default LoginPage;