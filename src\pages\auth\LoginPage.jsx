import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';
import { auth, googleProvider } from '../../firebase';
import { signInWithPopup } from 'firebase/auth';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    const success = login(email, password);
    if (success) {
      // Navigate to reading screen after successful login
      navigate('/readingScreen');
    } else {
      setError('Invalid credentials. Please try again.');
    }
  };

  const handleGoogleSignIn = () => {
    signInWithPopup(auth, googleProvider).then((result) => {
      useAuthStore.getState().thirdPartyLogin(result.user);
      navigate('/readingScreen');
      console.log("User signed in with Google", result.user);
    }).catch((error) => {
      console.log("Error signing in with Google", error);
    });
  };

  return (
    <div className="login-container">

      <Header />
      <AnimationBox className="login-box">

        <h4 className='h4'>Sign In</h4>

        <div className='social-login'>
          <button className='social-btn google body2 ' onClick={handleGoogleSignIn}>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='google' /></button>
          <button className='social-btn microsoft body2'>Continue with Microsoft<img src='/icons/microsoft.svg' alt='google' /></button>
          <button className='social-btn facebook body2'>Continue with Facebook<img src='/icons/facebook.svg' alt='google' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error.includes('email') && <div className="error-message">{error}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."
                required
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
            {error && !error.includes('email') && (
              <div className="error-message">{error}</div>)}
          </div>


          <button type="submit" className="body3-bold login-button">Sign in</button>
        </form>
        <div className='body4 forgot-password'><a href="/forgot-password">Forgot password?</a></div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>New to the platform?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/signup')}
          >
            Sign up
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default LoginPage;