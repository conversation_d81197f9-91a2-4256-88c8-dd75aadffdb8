import { useState } from 'react';
import { useAuthStore } from '../../stores/authStore';

/**
 * Debug component to display authentication information
 * This is useful for development and testing the unified authentication system
 */
const AuthDebugInfo = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { isAuthenticated, currentUser } = useAuthStore();
  const getUserAccountInfo = useAuthStore(state => state.getUserAccountInfo);
  const getAllUsers = useAuthStore(state => state.getAllUsers);
  const getLinkedProviders = useAuthStore(state => state.getLinkedProviders);

  if (!isAuthenticated || !currentUser) {
    return null;
  }

  const userInfo = getUserAccountInfo();
  const allUsers = getAllUsers();
  const linkedProviders = getLinkedProviders();

  const toggleExpand = () => setIsExpanded(!isExpanded);

  return (
    <div className="auth-debug-info" style={styles.container}>
      <button onClick={toggleExpand} style={styles.toggleButton}>
        {isExpanded ? 'Hide Auth Debug Info' : 'Show Auth Debug Info'}
      </button>

      {isExpanded && (
        <div style={styles.infoContainer}>
          <h3 style={styles.heading}>Current Authentication State</h3>
          <div style={styles.infoSection}>
            <p><strong>User ID:</strong> {currentUser.id}</p>
            <p><strong>Email:</strong> {currentUser.email}</p>
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>Linked Providers:</strong> {linkedProviders.join(', ') || 'None'}</p>
          </div>

          <h3 style={styles.heading}>Provider Details</h3>
          <div style={styles.infoSection}>
            {linkedProviders.map(provider => (
              <div key={provider} style={styles.providerInfo}>
                <h4>{provider}</h4>
                <pre style={styles.codeBlock}>
                  {JSON.stringify(userInfo?.providerData?.[provider] || {}, null, 2)}
                </pre>
              </div>
            ))}
          </div>

          <h3 style={styles.heading}>All Users (Debug)</h3>
          <div style={styles.infoSection}>
            <pre style={styles.codeBlock}>
              {JSON.stringify(allUsers, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

// Inline styles for the component
const styles = {
  container: {
    position: 'fixed',
    bottom: '10px',
    right: '10px',
    zIndex: 1000,
    backgroundColor: 'rgba(240, 240, 240, 0.9)',
    borderRadius: '5px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
    maxWidth: '400px',
    fontFamily: 'monospace',
    fontSize: '12px'
  },
  toggleButton: {
    padding: '8px 12px',
    backgroundColor: '#333',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    width: '100%'
  },
  infoContainer: {
    padding: '10px',
    maxHeight: '500px',
    overflowY: 'auto'
  },
  heading: {
    margin: '10px 0 5px',
    fontSize: '14px',
    borderBottom: '1px solid #ccc'
  },
  infoSection: {
    marginBottom: '15px'
  },
  providerInfo: {
    marginBottom: '10px'
  },
  codeBlock: {
    backgroundColor: '#f5f5f5',
    padding: '8px',
    borderRadius: '4px',
    overflow: 'auto',
    fontSize: '11px'
  }
};

export default AuthDebugInfo;
