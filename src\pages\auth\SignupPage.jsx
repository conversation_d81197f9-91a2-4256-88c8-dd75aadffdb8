import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';
import { auth, googleProvider, microsoftProvider } from '../../firebase';
import { signInWithPopup, fetchSignInMethodsForEmail } from 'firebase/auth';
import { useAuthStore } from '../../stores/authStore';

const SignupPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');

  const navigate = useNavigate();
  const { thirdPartyLogin } = useAuthStore();

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    // Navigate to OTP verification screen
    navigate('/otp-verification', { state: { email } });
  };

  // Generic OAuth sign-up handler (same as login - unified authentication)
  const handleOAuthSignUp = async (provider, providerName) => {
    try {
      setError('');
      const result = await signInWithPopup(auth, provider);

      if (!result.user.email) {
        setError(`Unable to use ${providerName} account without an email address.`);
        return;
      }

      // Check existing sign-in methods for this email
      try {
        const methods = await fetchSignInMethodsForEmail(auth, result.user.email);
        console.log(`Sign-in methods for ${result.user.email}:`, methods);
      } catch (methodsError) {
        console.warn("Error checking sign-in methods:", methodsError);
      }

      // Use our unified authentication system
      const user = thirdPartyLogin(
        result.user,
        provider === googleProvider ? 'google' : 'microsoft'
      );

      console.log(`User signed up with ${providerName}:`, {
        user,
        email: result.user.email,
        providers: user.providers,
        isNewUser: result.operationType === 'signIn' ? false : true
      });

      navigate('/readingScreen');
    } catch (error) {
      console.error(`Error signing up with ${providerName}:`, error);

      if (error.code === 'auth/account-exists-with-different-credential') {
        setError(`An account already exists with this email address.
                 Try signing in using the original provider you used.`);
      } else {
        setError(`Error signing up with ${providerName}: ${error.message}`);
      }
    }
  };

  // Provider-specific handlers
  const handleGoogleSignUp = () => handleOAuthSignUp(googleProvider, 'Google');
  const handleMicrosoftSignUp = () => handleOAuthSignUp(microsoftProvider, 'Microsoft');

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Sign Up</h4>

        <div className='social-login'>
          <button className='social-btn google body2' onClick={handleGoogleSignUp}>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='apple' /></button>
          <button className='social-btn microsoft body2' onClick={handleMicrosoftSignUp}>Continue with Microsoft<img src='/icons/microsoft.svg' alt='microsoft' /></button>
          <button className='social-btn facebook body2'>Continue with Facebook<img src='/icons/facebook.svg' alt='facebook' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error && <div className="error-message">{error}</div>}
          </div>

          <button type="submit" className="body3-bold login-button">Continue</button>
        </form>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>Already have an account?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/login')}
          >
            Sign in
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default SignupPage;