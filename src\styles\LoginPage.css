.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;



}


.login-box {
  position: relative;
  border-radius: 32px;
  margin: 1rem 2;
  border: 1px solid var(--border-color);
  max-width: 826px;
  width: 100%;
  z-index: 1;
  overflow: hidden;
  padding: 0;


  background-color: #FFFBF34D;

  backdrop-filter: blur(120px);
  -webkit-backdrop-filter: blur(120px);
  /* Safari support */
}


.h4 {
  margin-top: 0.0rem;
  margin-bottom: 0.5rem;
}



.social-login {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;

  border: 1px solid var(--border-color);
  background-color: transparent;
  border-radius: 10px;
  padding: 0.55rem 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.social-btn img {
  width: 20px;
  height: 20px;
  margin-left: 8px;
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  color: var(--border-color);
  margin: 1rem 0;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background: var(--border-color);
  margin: 0 10px;
}


.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.3rem;
  color: var(--text-color);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  background-color: var(--bg-color);
  color: var(--secondary-text-color);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
}

.form-group input:focus {
  outline: none;
  border-color: var(--secondary-text-color);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.password-wrapper {
  position: relative;
  width: 100%;
}

.password-wrapper input {
  padding: 0.75rem 2.5rem 0.75rem 1rem;

}

.toggle-password {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-text-color);
}


.error-message {
  color: #dc3545;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--text-color);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: 1rem 0;
}

/* .login-button:hover {
  background-color: #357abd;
} */

.forgot-password {
  text-align: center;

}

.forgot-password a {

  color: var(--text-color);
}

.hr {
  border: none;
  height: 1px;
  background-color: var(--border-color);
  margin: 1rem 0;

}

.signup {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.signup button {
  padding: 0.45rem 0.75rem;
  background-color: transparent;
  border: 1px solid var(--text-color);
  color: text-color;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}