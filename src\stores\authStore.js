import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Enhanced user data structure to support multiple auth providers
const USERS = [
  {
    id: 'user1',
    user: 'user1',
    email: '<EMAIL>',
    password: 'pass1',
    providers: ['email'], // Track which providers this user has used
    providerData: {
      email: { id: 'user1', createdAt: new Date().toISOString() }
    }
  },
  {
    id: 'user2',
    user: 'user2',
    email: '<EMAIL>',
    password: 'pass2',
    providers: ['email'],
    providerData: {
      email: { id: 'user2', createdAt: new Date().toISOString() }
    }
  }
];

// Helper function to generate unique user ID
const generateUserId = () => `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Helper function to find user by email (case-insensitive)
const findUserByEmail = (email) => {
  return USERS.find(user => user.email.toLowerCase() === email.toLowerCase());
};

// Helper function to create a new user
const createNewUser = (email, provider, providerData) => {
  const newUser = {
    id: generateUserId(),
    user: email.split('@')[0], // Use email prefix as username
    email: email.toLowerCase(),
    providers: [provider],
    providerData: {
      [provider]: {
        id: providerData.id || providerData.uid,
        createdAt: new Date().toISOString(),
        ...providerData
      }
    }
  };

  USERS.push(newUser);
  return newUser;
};

// Helper function to link provider to existing user
const linkProviderToUser = (user, provider, providerData) => {
  if (!user.providers.includes(provider)) {
    user.providers.push(provider);
    user.providerData[provider] = {
      id: providerData.id || providerData.uid,
      linkedAt: new Date().toISOString(),
      ...providerData
    };
  }
  return user;
};

export const useAuthStore = create(
  persist(
    (set, get) => ({
      currentUser: null,
      isAuthenticated: false,
      linkedAccounts: {}, // Map of email -> linked provider IDs

      // Standard email/password login
      login: (email, password) => {
        const user = USERS.find(
          (u) => u.email.toLowerCase() === email.toLowerCase() && u.password === password
        );
        if (user) {
          set({
            currentUser: user,
            isAuthenticated: true,
            // Track this login method
            lastLoginProvider: 'email'
          });
          return true;
        }
        return false;
      },

      // Enhanced third-party login with account linking
      thirdPartyLogin: (firebaseUser, provider) => {
        const email = firebaseUser.email.toLowerCase();
        const providerId = provider || firebaseUser.providerId || 'unknown';

        // Check if we already have a user with this email
        let user = findUserByEmail(email);

        if (user) {
          // Existing user - link this provider if not already linked
          user = linkProviderToUser(user, providerId, {
            uid: firebaseUser.uid,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            email: email
          });

          console.log(`Linked ${providerId} provider to existing account for ${email}`);
        } else {
          // New user - create account with this provider
          user = createNewUser(email, providerId, {
            uid: firebaseUser.uid,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            email: email
          });

          console.log(`Created new account for ${email} with ${providerId} provider`);
        }

        // Update linked accounts tracking
        const linkedAccounts = { ...get().linkedAccounts };
        if (!linkedAccounts[email]) {
          linkedAccounts[email] = [];
        }
        if (!linkedAccounts[email].includes(providerId)) {
          linkedAccounts[email].push(providerId);
        }

        // Set the current user and authentication state
        set({
          currentUser: user,
          isAuthenticated: true,
          linkedAccounts,
          lastLoginProvider: providerId
        });

        return user;
      },

      // Get all providers linked to the current user
      getLinkedProviders: () => {
        const { currentUser } = get();
        return currentUser ? currentUser.providers || [] : [];
      },

      // Check if a specific provider is linked to the current user
      isProviderLinked: (provider) => {
        const { currentUser } = get();
        return currentUser ?
          (currentUser.providers && currentUser.providers.includes(provider)) :
          false;
      },

      // Get user account information for debugging
      getUserAccountInfo: () => {
        const { currentUser, linkedAccounts } = get();
        if (!currentUser) return null;

        return {
          userId: currentUser.id,
          email: currentUser.email,
          providers: currentUser.providers,
          providerData: currentUser.providerData,
          allLinkedAccounts: linkedAccounts
        };
      },

      // Debug method to show all users (for development)
      getAllUsers: () => {
        return USERS.map(user => ({
          id: user.id,
          email: user.email,
          providers: user.providers,
          providerCount: user.providers ? user.providers.length : 0
        }));
      },

      // Standard logout
      logout: () => {
        set({
          currentUser: null,
          isAuthenticated: false,
          lastLoginProvider: null
        });
      }
    }),
    { name: 'auth-storage' }
  )
);
